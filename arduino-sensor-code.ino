/*
  Sensor Hujan untuk SMP PGRI 02 Cigombong
  Hardware yang dibutuhkan:
  - ESP32 atau ESP8266 (WiFi)
  - Sensor <PERSON> (Rain Sensor)
  - DHT22 (<PERSON><PERSON> & <PERSON><PERSON>)
  - Resistor 10kΩ
  - Breadboard & Kabel jumper
*/

#include <WiFi.h>
#include <WebServer.h>
#include <DHT.h>
#include <ArduinoJson.h>

// Konfigurasi WiFi
const char* ssid = "WIFI_SEKOLAH";
const char* password = "PASSWORD_WIFI";

// Pin Configuration
#define RAIN_SENSOR_PIN A0    // Pin analog untuk sensor hujan
#define DHT_PIN 2             // Pin digital untuk DHT22
#define DHT_TYPE DHT22

// Inisialisasi sensor dan server
DHT dht(DHT_PIN, DHT_TYPE);
WebServer server(80);

// Variabel sensor
float temperature = 0;
float humidity = 0;
int rainValue = 0;
bool isRaining = false;
int rainIntensity = 0;

void setup() {
  Serial.begin(115200);
  
  // Inisialisasi sensor
  dht.begin();
  pinMode(RAIN_SENSOR_PIN, INPUT);
  
  // Koneksi WiFi
  WiFi.begin(ssid, password);
  Serial.print("Connecting to WiFi");
  
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  
  Serial.println();
  Serial.print("Connected! IP address: ");
  Serial.println(WiFi.localIP());
  
  // Setup web server endpoints
  server.on("/", handleRoot);
  server.on("/api/weather", handleWeatherAPI);
  server.on("/api/data", HTTP_GET, handleGetData);
  
  // Enable CORS untuk akses dari web app
  server.enableCORS(true);
  
  server.begin();
  Serial.println("Web server started");
}

void loop() {
  server.handleClient();
  
  // Baca sensor setiap 5 detik
  static unsigned long lastReading = 0;
  if (millis() - lastReading > 5000) {
    readSensors();
    lastReading = millis();
  }
}

void readSensors() {
  // Baca DHT22
  temperature = dht.readTemperature();
  humidity = dht.readHumidity();
  
  // Baca sensor hujan
  rainValue = analogRead(RAIN_SENSOR_PIN);
  
  // Konversi nilai sensor hujan
  // Nilai tinggi = tidak hujan, nilai rendah = hujan
  if (rainValue < 500) {
    isRaining = true;
    rainIntensity = map(500 - rainValue, 0, 500, 20, 100);
  } else {
    isRaining = false;
    rainIntensity = 0;
  }
  
  // Debug output
  Serial.printf("Temp: %.1f°C, Humidity: %.1f%%, Rain: %d, Intensity: %d%%\n", 
                temperature, humidity, rainValue, rainIntensity);
}

void handleRoot() {
  String html = R"(
<!DOCTYPE html>
<html>
<head>
    <title>Sensor Hujan - SMP PGRI 02 Cigombong</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial; margin: 20px; background: #f0f0f0; }
        .container { background: white; padding: 20px; border-radius: 10px; }
        .sensor-data { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }
        .data-card { background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center; }
        .value { font-size: 2em; font-weight: bold; color: #1976d2; }
        .label { color: #666; margin-top: 5px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; text-align: center; font-weight: bold; }
        .raining { background: #ffcdd2; color: #c62828; }
        .clear { background: #c8e6c9; color: #2e7d32; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌧️ Sensor Hujan - SMP PGRI 02 Cigombong</h1>
        <div id="status" class="status">Loading...</div>
        <div class="sensor-data">
            <div class="data-card">
                <div class="value" id="temp">--</div>
                <div class="label">Suhu (°C)</div>
            </div>
            <div class="data-card">
                <div class="value" id="humidity">--</div>
                <div class="label">Kelembaban (%)</div>
            </div>
            <div class="data-card">
                <div class="value" id="intensity">--</div>
                <div class="label">Intensitas Hujan (%)</div>
            </div>
        </div>
        <p><strong>IP Address:</strong> )" + WiFi.localIP().toString() + R"(</p>
        <p><strong>API Endpoint:</strong> /api/weather</p>
    </div>
    
    <script>
        function updateData() {
            fetch('/api/weather')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('temp').textContent = data.temperature.toFixed(1);
                    document.getElementById('humidity').textContent = data.humidity.toFixed(1);
                    document.getElementById('intensity').textContent = data.intensity;
                    
                    const status = document.getElementById('status');
                    if (data.isRaining) {
                        status.textContent = '🌧️ Sedang Hujan';
                        status.className = 'status raining';
                    } else {
                        status.textContent = '☀️ Tidak Hujan';
                        status.className = 'status clear';
                    }
                })
                .catch(error => console.error('Error:', error));
        }
        
        // Update setiap 5 detik
        setInterval(updateData, 5000);
        updateData(); // Initial load
    </script>
</body>
</html>
  )";
  
  server.send(200, "text/html", html);
}

void handleWeatherAPI() {
  // Buat JSON response
  StaticJsonDocument<200> doc;
  doc["temperature"] = temperature;
  doc["humidity"] = humidity;
  doc["isRaining"] = isRaining;
  doc["intensity"] = rainIntensity;
  doc["rawRainValue"] = rainValue;
  doc["timestamp"] = millis();
  doc["location"] = "SMP PGRI 02 Cigombong";
  
  String response;
  serializeJson(doc, response);
  
  server.send(200, "application/json", response);
}

void handleGetData() {
  // Endpoint untuk integrasi dengan web app
  handleWeatherAPI();
}
