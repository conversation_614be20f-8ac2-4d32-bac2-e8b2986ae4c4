# 📋 Technical Specifications - Weather Monitoring System

## 🏗️ System Architecture

### Frontend
- **Framework**: Vanilla JavaScript (ES6+)
- **Styling**: CSS3 with Flexbox/Grid
- **Charts**: Chart.js v4.4.0
- **Icons**: Font Awesome 6.0
- **Fonts**: Google Fonts (Poppins)

### Data Sources
- **Simulation Mode**: Built-in weather simulation
- **API Mode**: OpenWeatherMap API integration
- **Hardware Mode**: ESP32/Arduino sensor integration
- **Hybrid Mode**: API + Hardware with fallback

### Browser Compatibility
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+
- Mobile browsers (iOS Safari, Chrome Mobile)

## 🔧 Technical Features

### Real-time Updates
- **Update Interval**: 5 seconds (simulation) / 10 minutes (API) / 5 seconds (hardware)
- **Data Retention**: 24 hours rolling window
- **Chart Updates**: Live data visualization
- **Responsive UI**: Automatic layout adjustment

### Performance Optimization
- **Lazy Loading**: Chart initialization on demand
- **Error Handling**: Graceful fallback systems
- **Memory Management**: Automatic data cleanup
- **Caching**: Local storage for preferences

### Security Features
- **API Key Protection**: Environment variable support
- **CORS Handling**: Cross-origin request management
- **Input Validation**: Data sanitization
- **Error Logging**: Console-based debugging

## 📊 Data Structure

### Sensor Data Format
```javascript
{
    time: Date,           // Timestamp
    isRaining: Boolean,   // Rain status
    intensity: Number,    // Rain intensity (0-100%)
    temperature: Number,  // Temperature in Celsius
    humidity: Number,     // Relative humidity (0-100%)
    source: String       // Data source (simulation/api/hardware)
}
```

### API Response Format
```javascript
{
    temperature: Number,
    humidity: Number,
    isRaining: Boolean,
    intensity: Number,
    description: String,
    timestamp: Date,
    location: String
}
```

## 🌐 API Integration

### OpenWeatherMap API
- **Endpoint**: `https://api.openweathermap.org/data/2.5/weather`
- **Rate Limit**: 1000 calls/day (free tier)
- **Update Frequency**: 10 minutes
- **Data Coverage**: Global weather data

### Required Parameters
```javascript
{
    lat: Number,      // Latitude
    lon: Number,      // Longitude
    appid: String,    // API key
    units: 'metric',  // Temperature unit
    lang: 'id'        // Language (Indonesian)
}
```

## 🔌 Hardware Integration

### Supported Hardware
- **ESP32 DevKit**: Primary microcontroller
- **ESP8266**: Alternative option
- **Arduino Uno + WiFi Shield**: Basic option

### Sensor Requirements
- **Rain Sensor**: Analog rain detection module
- **DHT22**: Temperature and humidity sensor
- **Power Supply**: 5V/3.3V depending on board

### Communication Protocol
- **Method**: HTTP REST API
- **Format**: JSON
- **Port**: 80 (HTTP)
- **Endpoints**: `/api/weather`, `/api/data`

### Network Configuration
```cpp
// WiFi Settings
const char* ssid = "SCHOOL_WIFI";
const char* password = "WIFI_PASSWORD";

// Server Settings
WebServer server(80);
IPAddress local_IP(192, 168, 1, 100);
```

## 📱 Responsive Design

### Breakpoints
- **Desktop**: > 1024px
- **Tablet**: 768px - 1024px
- **Mobile**: < 768px
- **Small Mobile**: < 480px

### Mobile Features
- **Hamburger Menu**: Collapsible navigation
- **Touch Optimization**: 44px minimum touch targets
- **Swipe Gestures**: Chart interaction
- **Viewport Optimization**: Proper scaling

## 🔄 Data Flow

### Simulation Mode
```
Timer (5s) → Generate Random Data → Update UI → Store History
```

### API Mode
```
Timer (10min) → Fetch API → Process Data → Update UI → Store History
```

### Hardware Mode
```
Sensor Reading → ESP32 → HTTP Request → Process Data → Update UI
```

### Hybrid Mode
```
Try Hardware → If Fail → Try API → If Fail → Use Simulation
```

## 🛠️ Installation & Deployment

### Local Development
1. Clone repository
2. Open `index.html` in browser
3. No build process required

### Web Server Deployment
1. Upload files to web server
2. Configure API keys (if using API mode)
3. Set proper MIME types for .js files
4. Enable HTTPS for production

### CDN Dependencies
- Chart.js: `https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js`
- Font Awesome: `https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css`
- Google Fonts: `https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap`

## 🔍 Monitoring & Debugging

### Console Logging
- **Info**: Data updates and status changes
- **Warnings**: API failures and retries
- **Errors**: Critical failures and fallbacks

### Performance Metrics
- **Load Time**: < 2 seconds initial load
- **Update Latency**: < 500ms UI updates
- **Memory Usage**: < 50MB typical usage
- **Network Usage**: < 1MB/hour (API mode)

### Error Handling
- **Network Errors**: Automatic retry with exponential backoff
- **API Errors**: Fallback to simulation mode
- **Hardware Errors**: Switch to API mode
- **Chart Errors**: Graceful degradation

## 🔒 Security Considerations

### API Security
- Store API keys in environment variables
- Use HTTPS for all API calls
- Implement rate limiting
- Validate all incoming data

### Hardware Security
- Use WPA2/WPA3 for WiFi
- Implement basic authentication
- Regular firmware updates
- Network isolation

### Client Security
- Input sanitization
- XSS prevention
- Content Security Policy
- Secure cookie handling

## 📈 Scalability

### Performance Optimization
- **Lazy Loading**: Load components on demand
- **Data Pagination**: Limit historical data
- **Caching**: Browser and server-side caching
- **Compression**: Gzip for static assets

### Multi-location Support
- **Database**: Centralized data storage
- **API**: RESTful endpoints for multiple sensors
- **UI**: Location selector and comparison
- **Real-time**: WebSocket for live updates

## 🧪 Testing

### Browser Testing
- Cross-browser compatibility testing
- Mobile device testing
- Performance testing
- Accessibility testing

### API Testing
- Rate limit testing
- Error response handling
- Data validation testing
- Timeout handling

### Hardware Testing
- Sensor accuracy validation
- Network connectivity testing
- Power consumption testing
- Environmental testing

---

**Last Updated**: January 2025  
**Version**: 1.0.0  
**Compatibility**: Modern browsers, ES6+ support required
