# 🌧️ Sensor Hujan - Dashboard Sekolah

Proyek Sensor Hujan ini adalah aplikasi web edukatif yang dirancang khusus untuk lingkungan sekolahan. Aplikasi ini memungkinkan siswa dan guru untuk memantau kondisi cuaca secara real-time sambil belajar tentang teknologi sensor dan meteorologi.

## ✨ Fitur Utama

### 📊 Dashboard Real-time
- **Status Hujan**: Indikator visual yang menunjukkan apakah sedang hujan atau tidak
- **Intensitas Hujan**: Meter yang menampilkan tingkat intensitas hujan (ringan, sedang, lebat)
- **Suhu Udara**: Monitoring suhu dengan deskripsi kondisi (dingin, sejuk, nyaman, panas)
- **Kelembaban**: Tampilan circular progress untuk kelembaban udara

### 📈 Visualisasi Data
- **Grafik Real-time**: Chart interaktif yang menampilkan data 24 jam terakhir
- **Tabel Data**: Riwayat data sensor dengan timestamp
- **Animasi Cuaca**: Ikon cuaca yang berubah sesuai kondisi

### 🎓 Konten Edukatif
- **Pembentukan Hu<PERSON>**: Penjelasan siklus air dan proses terjadinya hujan
- **Pentingnya Suhu**: Informasi tentang pengaruh suhu terhadap kehidupan
- **Kelembaban Udara**: Edukasi tentang kelembaban dan dampaknya

### 📱 Desain Responsif
- Tampilan optimal di desktop, tablet, dan smartphone
- Interface yang ramah untuk siswa dengan warna-warna menarik
- Navigasi yang mudah dan intuitif

## 🚀 Cara Menjalankan

1. **Download atau Clone** proyek ini ke komputer Anda
2. **Buka file `index.html`** di web browser
3. **Nikmati** dashboard sensor hujan yang interaktif!

### Persyaratan Sistem
- Web browser modern (Chrome, Firefox, Safari, Edge)
- Koneksi internet untuk memuat font dan ikon eksternal
- Tidak memerlukan server khusus (dapat dijalankan secara lokal)

## 📁 Struktur File

```
sensor_hujan/
├── index.html          # File HTML utama
├── styles.css          # Styling dan desain
├── script.js           # Logika JavaScript dan simulasi data
└── README.md           # Dokumentasi proyek
```

## 🎨 Desain UI/UX

### Palet Warna
- **Primary**: Gradient biru-ungu (#667eea - #764ba2)
- **Success**: Hijau (#28a745) untuk kondisi normal
- **Warning**: Kuning (#ffc107) untuk peringatan
- **Danger**: Merah (#dc3545) untuk kondisi hujan
- **Background**: Putih transparan dengan efek blur

### Typography
- **Font**: Poppins (Google Fonts)
- **Weights**: 300, 400, 600, 700
- **Hierarchy**: Jelas dengan ukuran yang berbeda untuk setiap level

### Komponen UI
- **Cards**: Rounded corners dengan shadow dan hover effects
- **Buttons**: Gradient dengan animasi hover
- **Icons**: Font Awesome untuk konsistensi
- **Animations**: Smooth transitions dan floating effects

## 🔧 Fitur Teknis

### Simulasi Data Sensor
- Data sensor disimulasi secara real-time setiap 5 detik
- Algoritma random yang realistis untuk:
  - Status hujan (20% kemungkinan hujan)
  - Suhu (range 20-35°C)
  - Kelembaban (range 30-90%)
  - Intensitas hujan (0-100%)

### Visualisasi Data
- **Chart.js** untuk grafik interaktif
- **CSS Animations** untuk efek visual
- **SVG Progress Circles** untuk kelembaban
- **Responsive Grid Layout** untuk card system

### Interaktivitas
- **Smooth Scrolling** navigation
- **Modal Dialogs** untuk konten edukatif
- **Real-time Updates** untuk semua data
- **Hover Effects** pada semua elemen interaktif

## 🎯 Tujuan Edukatif

### Untuk Siswa
- Memahami cara kerja sensor dalam teknologi modern
- Belajar tentang meteorologi dan cuaca
- Mengenal konsep monitoring dan data visualization
- Mengembangkan minat terhadap STEM

### Untuk Guru
- Tool pembelajaran interaktif untuk mata pelajaran IPA
- Demonstrasi praktis tentang teknologi sensor
- Media untuk menjelaskan konsep cuaca dan iklim
- Platform untuk diskusi tentang teknologi modern

## 🔮 Pengembangan Selanjutnya

### Fitur yang Bisa Ditambahkan
- **Koneksi Sensor Real**: Integrasi dengan sensor Arduino/Raspberry Pi
- **Database**: Penyimpanan data historis yang persisten
- **Notifikasi**: Alert ketika kondisi cuaca berubah
- **Multi-location**: Monitoring beberapa lokasi sekaligus
- **Export Data**: Fitur download data dalam format CSV/Excel
- **User Management**: Sistem login untuk guru dan siswa

### Integrasi Hardware
- **Arduino + Rain Sensor**: Untuk deteksi hujan real
- **DHT22**: Sensor suhu dan kelembaban
- **ESP32/ESP8266**: Untuk konektivitas WiFi
- **API Integration**: Menghubungkan dengan layanan cuaca online

## 📚 Sumber Belajar

### Teknologi yang Digunakan
- **HTML5**: Struktur dan semantik web modern
- **CSS3**: Styling dengan Flexbox, Grid, dan Animations
- **JavaScript ES6+**: Logika aplikasi dan DOM manipulation
- **Chart.js**: Library untuk visualisasi data
- **Font Awesome**: Icon library
- **Google Fonts**: Typography

### Konsep yang Dipelajari
- **Responsive Web Design**: Mobile-first approach
- **Data Visualization**: Cara menyajikan data yang menarik
- **User Experience (UX)**: Desain yang user-friendly
- **Real-time Updates**: Simulasi data sensor
- **Educational Technology**: Gamifikasi pembelajaran

## 🤝 Kontribusi

Proyek ini terbuka untuk pengembangan lebih lanjut. Beberapa area yang bisa dikembangkan:

1. **Peningkatan UI/UX**: Animasi yang lebih menarik
2. **Fitur Edukatif**: Konten pembelajaran yang lebih lengkap
3. **Aksesibilitas**: Dukungan untuk pengguna dengan kebutuhan khusus
4. **Internationalization**: Dukungan multi-bahasa
5. **Performance**: Optimasi loading dan rendering

## 📄 Lisensi

Proyek ini dibuat untuk tujuan edukatif dan dapat digunakan secara bebas untuk pembelajaran di sekolah-sekolah.

---

**Dibuat dengan ❤️ untuk pendidikan Indonesia**

*Proyek ini bertujuan untuk memperkenalkan teknologi sensor dan monitoring cuaca kepada siswa dengan cara yang menyenangkan dan interaktif.*
