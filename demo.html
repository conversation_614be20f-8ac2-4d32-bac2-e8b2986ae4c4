<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - Sensor <PERSON></title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .demo-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }
        
        .demo-section h2 {
            color: #333;
            margin-top: 0;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✅ ";
            margin-right: 0.5rem;
        }
        
        .btn {
            display: inline-block;
            background: #667eea;
            color: white;
            padding: 1rem 2rem;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            text-align: center;
            transition: all 0.3s ease;
            margin: 1rem 0;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .screenshot {
            width: 100%;
            max-width: 600px;
            height: 300px;
            background: linear-gradient(45deg, #e9ecef, #f8f9fa);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 1rem 0;
            border: 2px dashed #667eea;
            color: #667eea;
            font-size: 1.2rem;
        }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        
        .tech-item {
            background: white;
            padding: 1rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .tech-item strong {
            color: #667eea;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .warning strong {
            color: #856404;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 0 10px;
                padding: 1rem;
            }
            
            .tech-stack {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌧️ Demo Sensor Hujan Sekolah</h1>
        
        <div class="demo-section">
            <h2>📋 Tentang Proyek</h2>
            <p>Sensor Hujan Sekolah adalah aplikasi web edukatif yang dirancang khusus untuk lingkungan pendidikan. Aplikasi ini memungkinkan siswa dan guru untuk memahami teknologi sensor sambil memantau kondisi cuaca secara real-time.</p>
            
            <div class="warning">
                <strong>📝 Catatan:</strong> Ini adalah versi demo dengan data simulasi. Untuk implementasi dengan sensor nyata, diperlukan integrasi hardware tambahan.
            </div>
        </div>
        
        <div class="demo-section">
            <h2>✨ Fitur Utama</h2>
            <ul class="feature-list">
                <li>Dashboard real-time dengan data sensor cuaca</li>
                <li>Visualisasi data dengan grafik interaktif</li>
                <li>Konten edukatif tentang meteorologi</li>
                <li>Interface responsif untuk semua perangkat</li>
                <li>Animasi dan efek visual yang menarik</li>
                <li>Simulasi data sensor yang realistis</li>
                <li>Riwayat data 24 jam terakhir</li>
                <li>Modal edukatif untuk pembelajaran</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2>🖥️ Preview Aplikasi</h2>
            <div class="screenshot">
                📱 Screenshot Dashboard akan ditampilkan di sini
            </div>
            <p><em>Dashboard menampilkan status hujan, suhu, kelembaban, dan intensitas hujan dalam tampilan yang menarik dan mudah dipahami.</em></p>
        </div>
        
        <div class="demo-section">
            <h2>🛠️ Teknologi yang Digunakan</h2>
            <div class="tech-stack">
                <div class="tech-item">
                    <strong>HTML5</strong><br>
                    Struktur semantik
                </div>
                <div class="tech-item">
                    <strong>CSS3</strong><br>
                    Styling modern
                </div>
                <div class="tech-item">
                    <strong>JavaScript</strong><br>
                    Interaktivitas
                </div>
                <div class="tech-item">
                    <strong>Chart.js</strong><br>
                    Visualisasi data
                </div>
                <div class="tech-item">
                    <strong>Font Awesome</strong><br>
                    Ikon modern
                </div>
                <div class="tech-item">
                    <strong>Google Fonts</strong><br>
                    Typography
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🎯 Tujuan Edukatif</h2>
            <p><strong>Untuk Siswa:</strong></p>
            <ul>
                <li>Memahami cara kerja sensor dalam teknologi modern</li>
                <li>Belajar tentang meteorologi dan cuaca</li>
                <li>Mengenal konsep monitoring dan visualisasi data</li>
                <li>Mengembangkan minat terhadap STEM</li>
            </ul>
            
            <p><strong>Untuk Guru:</strong></p>
            <ul>
                <li>Tool pembelajaran interaktif untuk mata pelajaran IPA</li>
                <li>Media demonstrasi teknologi sensor</li>
                <li>Platform diskusi tentang cuaca dan iklim</li>
                <li>Contoh implementasi teknologi dalam pendidikan</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2>🚀 Cara Menjalankan</h2>
            <ol>
                <li><strong>Download</strong> semua file proyek</li>
                <li><strong>Buka</strong> file <code>index.html</code> di web browser</li>
                <li><strong>Nikmati</strong> dashboard sensor hujan yang interaktif!</li>
            </ol>
            
            <p><strong>Persyaratan:</strong></p>
            <ul>
                <li>Web browser modern (Chrome, Firefox, Safari, Edge)</li>
                <li>Koneksi internet untuk font dan ikon eksternal</li>
                <li>Tidak memerlukan server khusus</li>
            </ul>
        </div>
        
        <div class="demo-section">
            <h2>🔮 Pengembangan Selanjutnya</h2>
            <p>Proyek ini dapat dikembangkan lebih lanjut dengan:</p>
            <ul>
                <li><strong>Hardware Integration:</strong> Koneksi dengan sensor Arduino/Raspberry Pi</li>
                <li><strong>Database:</strong> Penyimpanan data historis yang persisten</li>
                <li><strong>API Integration:</strong> Koneksi dengan layanan cuaca online</li>
                <li><strong>Multi-location:</strong> Monitoring beberapa lokasi sekaligus</li>
                <li><strong>Notifications:</strong> Alert untuk perubahan cuaca</li>
                <li><strong>Data Export:</strong> Download data dalam format CSV/Excel</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin-top: 2rem;">
            <a href="index.html" class="btn">🚀 Buka Aplikasi Sensor Hujan</a>
        </div>
        
        <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e9ecef; color: #666;">
            <p><strong>Dibuat dengan ❤️ untuk pendidikan Indonesia</strong></p>
            <p><em>Proyek ini bertujuan memperkenalkan teknologi sensor kepada siswa dengan cara yang menyenangkan dan interaktif.</em></p>
        </div>
    </div>
</body>
</html>
