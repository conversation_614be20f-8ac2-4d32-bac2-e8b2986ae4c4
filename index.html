<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sensor <PERSON>jan - Dashboard Sekolah</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header" role="banner">
        <div class="container">
            <div class="logo">
                <i class="fas fa-cloud-rain" aria-hidden="true"></i>
                <h1>Sensor <PERSON></h1>
            </div>

            <!-- Mobile Menu Button -->
            <button class="mobile-menu-btn" aria-label="Toggle menu" aria-expanded="false">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>

            <nav class="nav" role="navigation" aria-label="Menu utama">
                <a href="#dashboard" class="nav-link active" aria-current="page">Dashboard</a>
                <a href="#data" class="nav-link">Data</a>
                <a href="#edukasi" class="nav-link">Edukasi</a>
                <a href="#tentang" class="nav-link">Tentang</a>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main" role="main">
        <!-- Hero Section -->
        <section class="hero">
            <div class="container">
                <div class="hero-content">
                    <h2>Monitoring Cuaca Real-time</h2>
                    <p>Pantau kondisi cuaca di lingkungan sekolah dengan teknologi sensor modern</p>
                    <div class="weather-animation" aria-label="Animasi cuaca">
                        <div class="cloud" id="weatherIcon">
                            <i class="fas fa-sun" aria-label="Ikon cuaca saat ini"></i>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Dashboard Cards -->
        <section class="dashboard" id="dashboard" aria-labelledby="dashboard-title">
            <div class="container">
                <h3 id="dashboard-title">Status Sensor Saat Ini</h3>
                <div class="cards-grid" role="region" aria-label="Data sensor cuaca">
                    <!-- Status Hujan Card -->
                    <div class="card rain-status" role="article" aria-labelledby="rain-title">
                        <div class="card-header">
                            <i class="fas fa-tint" aria-hidden="true"></i>
                            <h4 id="rain-title">Status Hujan</h4>
                        </div>
                        <div class="card-body">
                            <div class="status-indicator" id="rainStatus" role="status" aria-live="polite">
                                <div class="indicator-light" aria-hidden="true"></div>
                                <span id="rainText">Tidak Hujan</span>
                            </div>
                            <div class="last-update">
                                Terakhir update: <span id="lastUpdate" aria-live="polite">--:--</span>
                            </div>
                        </div>
                    </div>

                    <!-- Intensitas Card -->
                    <div class="card intensity">
                        <div class="card-header">
                            <i class="fas fa-thermometer-half"></i>
                            <h4>Intensitas Hujan</h4>
                        </div>
                        <div class="card-body">
                            <div class="intensity-meter">
                                <div class="meter-bar">
                                    <div class="meter-fill" id="intensityFill"></div>
                                </div>
                                <div class="intensity-labels">
                                    <span>Ringan</span>
                                    <span>Sedang</span>
                                    <span>Lebat</span>
                                </div>
                            </div>
                            <div class="intensity-value">
                                <span id="intensityValue">0</span>%
                            </div>
                        </div>
                    </div>

                    <!-- Suhu Card -->
                    <div class="card temperature">
                        <div class="card-header">
                            <i class="fas fa-temperature-high"></i>
                            <h4>Suhu Udara</h4>
                        </div>
                        <div class="card-body">
                            <div class="temp-display">
                                <span class="temp-value" id="tempValue">25</span>
                                <span class="temp-unit">°C</span>
                            </div>
                            <div class="temp-description" id="tempDesc">Nyaman</div>
                        </div>
                    </div>

                    <!-- Kelembaban Card -->
                    <div class="card humidity">
                        <div class="card-header">
                            <i class="fas fa-water"></i>
                            <h4>Kelembaban</h4>
                        </div>
                        <div class="card-body">
                            <div class="humidity-circle">
                                <svg class="circle-progress" viewBox="0 0 100 100">
                                    <circle cx="50" cy="50" r="45" class="circle-bg"></circle>
                                    <circle cx="50" cy="50" r="45" class="circle-fill" id="humidityCircle"></circle>
                                </svg>
                                <div class="humidity-text">
                                    <span id="humidityValue">60</span>%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Data History Section -->
        <section class="data-section" id="data">
            <div class="container">
                <h3>Riwayat Data Sensor</h3>
                <div class="chart-container">
                    <canvas id="weatherChart"></canvas>
                </div>
                <div class="data-table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Waktu</th>
                                <th>Status Hujan</th>
                                <th>Intensitas</th>
                                <th>Suhu (°C)</th>
                                <th>Kelembaban (%)</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <!-- Data akan diisi oleh JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </section>

        <!-- Educational Section -->
        <section class="education-section" id="edukasi">
            <div class="container">
                <h3>Mari Belajar Tentang Cuaca!</h3>
                <div class="education-grid">
                    <div class="edu-card">
                        <div class="edu-icon">
                            <i class="fas fa-cloud-rain"></i>
                        </div>
                        <h4>Bagaimana Hujan Terbentuk?</h4>
                        <p>Hujan terbentuk melalui proses evaporasi, kondensasi, dan presipitasi dalam siklus air.</p>
                        <button class="learn-more-btn" onclick="showModal('rain-formation')">Pelajari Lebih Lanjut</button>
                    </div>
                    
                    <div class="edu-card">
                        <div class="edu-icon">
                            <i class="fas fa-thermometer-half"></i>
                        </div>
                        <h4>Mengapa Suhu Penting?</h4>
                        <p>Suhu mempengaruhi kenyamanan, aktivitas, dan berbagai proses alam di sekitar kita.</p>
                        <button class="learn-more-btn" onclick="showModal('temperature')">Pelajari Lebih Lanjut</button>
                    </div>
                    
                    <div class="edu-card">
                        <div class="edu-icon">
                            <i class="fas fa-tint"></i>
                        </div>
                        <h4>Apa itu Kelembaban?</h4>
                        <p>Kelembaban adalah jumlah uap air yang terkandung dalam udara di sekitar kita.</p>
                        <button class="learn-more-btn" onclick="showModal('humidity')">Pelajari Lebih Lanjut</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about-section" id="tentang">
            <div class="container">
                <h3>Tentang Proyek Sensor Hujan</h3>
                <div class="about-content">
                    <div class="about-text">
                        <p>Proyek Sensor Hujan ini dibuat untuk membantu siswa memahami teknologi sensor dan monitoring cuaca. Dengan menggunakan sensor modern, kita dapat memantau kondisi cuaca secara real-time dan belajar tentang pola cuaca di lingkungan sekolah.</p>
                        <div class="features">
                            <div class="feature">
                                <i class="fas fa-check-circle"></i>
                                <span>Monitoring real-time</span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-check-circle"></i>
                                <span>Data historis</span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-check-circle"></i>
                                <span>Konten edukatif</span>
                            </div>
                            <div class="feature">
                                <i class="fas fa-check-circle"></i>
                                <span>Interface ramah pengguna</span>
                            </div>
                        </div>
                    </div>
                    <div class="about-image">
                        <div class="sensor-illustration">
                            <i class="fas fa-microchip"></i>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Sensor Hujan Sekolah. Dibuat untuk pembelajaran teknologi sensor.</p>
        </div>
    </footer>

    <!-- Modal for Educational Content -->
    <div class="modal" id="educationModal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <div id="modalContent">
                <!-- Content will be loaded by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script>
        // Check if Chart.js loaded
        if (typeof Chart === 'undefined') {
            console.error('Chart.js failed to load');
        } else {
            console.log('Chart.js loaded successfully');
        }
    </script>
    <script src="script.js"></script>
</body>
</html>
