/* Reset dan Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #667eea;
}

.logo i {
    font-size: 2rem;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background: #667eea;
    color: white;
}

/* Main Content */
.main {
    margin-top: 80px;
    padding: 2rem 0;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 3rem 0;
    color: white;
}

.hero-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.weather-animation {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.cloud {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 2rem;
    animation: float 3s ease-in-out infinite;
}

.cloud i {
    font-size: 3rem;
    color: #ffd700;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Dashboard Cards */
.dashboard {
    padding: 3rem 0;
}

.dashboard h3 {
    text-align: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 2rem;
}

.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1.5rem;
}

.card-header i {
    font-size: 1.5rem;
    color: #667eea;
}

.card-header h4 {
    font-size: 1.2rem;
    font-weight: 600;
}

/* Rain Status Card */
.rain-status .status-indicator {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 1rem;
}

.indicator-light {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #28a745;
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
    animation: pulse 2s infinite;
}

.indicator-light.raining {
    background: #dc3545;
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.last-update {
    font-size: 0.9rem;
    color: #666;
}

/* Intensity Card */
.intensity-meter {
    margin-bottom: 1rem;
}

.meter-bar {
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.meter-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
    width: 0%;
    transition: width 0.5s ease;
    border-radius: 10px;
}

.intensity-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #666;
}

.intensity-value {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    color: #667eea;
}

/* Temperature Card */
.temp-display {
    text-align: center;
    margin-bottom: 1rem;
}

.temp-value {
    font-size: 3rem;
    font-weight: 700;
    color: #667eea;
}

.temp-unit {
    font-size: 1.5rem;
    color: #666;
}

.temp-description {
    text-align: center;
    font-size: 1.1rem;
    color: #28a745;
    font-weight: 500;
}

/* Humidity Card */
.humidity-circle {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 0 auto;
}

.circle-progress {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.circle-bg {
    fill: none;
    stroke: #e9ecef;
    stroke-width: 8;
}

.circle-fill {
    fill: none;
    stroke: #667eea;
    stroke-width: 8;
    stroke-linecap: round;
    stroke-dasharray: 283;
    stroke-dashoffset: 283;
    transition: stroke-dashoffset 0.5s ease;
}

.humidity-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}

.humidity-text span {
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

/* Data Section */
.data-section {
    background: rgba(255, 255, 255, 0.95);
    margin: 2rem 0;
    border-radius: 20px;
    padding: 3rem 0;
}

.data-section h3 {
    text-align: center;
    color: #333;
    font-size: 2rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.data-table-container {
    overflow-x: auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* Education Section */
.education-section {
    padding: 3rem 0;
}

.education-section h3 {
    text-align: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 2rem;
}

.education-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.edu-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.edu-card:hover {
    transform: translateY(-5px);
}

.edu-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.edu-card h4 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.edu-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.learn-more-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.learn-more-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

/* About Section */
.about-section {
    background: rgba(255, 255, 255, 0.95);
    margin: 2rem 0;
    border-radius: 20px;
    padding: 3rem 0;
}

.about-section h3 {
    text-align: center;
    color: #333;
    font-size: 2rem;
    margin-bottom: 2rem;
}

.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
    margin-bottom: 2rem;
}

.features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 10px;
}

.feature i {
    color: #28a745;
    font-size: 1.2rem;
}

.sensor-illustration {
    text-align: center;
    font-size: 8rem;
    color: #667eea;
    opacity: 0.7;
}

/* Footer */
.footer {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 2rem;
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 2rem;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    position: relative;
}

.close {
    position: absolute;
    right: 1rem;
    top: 1rem;
    font-size: 2rem;
    cursor: pointer;
    color: #666;
}

.close:hover {
    color: #333;
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for keyboard navigation */
.nav-link:focus,
.learn-more-btn:focus,
.close:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #333;
    }

    .nav-link:hover,
    .nav-link.active {
        border: 2px solid #fff;
    }
}

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .cloud {
        animation: none;
    }

    .indicator-light {
        animation: none;
    }
}

/* Improved color contrast */
.data-table th {
    background: #e9ecef;
    color: #212529;
    font-weight: 600;
}

.temp-description {
    font-weight: 600;
}

/* Better touch targets for mobile */
@media (max-width: 768px) {
    .nav-link {
        padding: 0.75rem 1.25rem;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .learn-more-btn {
        padding: 1rem 2rem;
        min-height: 44px;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .header .container {
        flex-direction: column;
        gap: 1rem;
    }

    .nav {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .hero-content h2 {
        font-size: 2rem;
    }

    .cards-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .features {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .education-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .chart-container {
        padding: 1rem;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .card {
        padding: 1.5rem;
    }

    .hero-content h2 {
        font-size: 1.8rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .logo h1 {
        font-size: 1.2rem;
    }

    .nav {
        gap: 0.5rem;
    }

    .nav-link {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .temp-value {
        font-size: 2.5rem;
    }

    .humidity-circle {
        width: 100px;
        height: 100px;
    }

    .humidity-text span {
        font-size: 1.2rem;
    }

    .edu-card {
        padding: 1.5rem;
    }

    .edu-icon {
        font-size: 2.5rem;
    }

    .sensor-illustration {
        font-size: 6rem;
    }
}

/* Extra small devices */
@media (max-width: 320px) {
    .hero-content h2 {
        font-size: 1.5rem;
    }

    .card {
        padding: 1rem;
    }

    .temp-value {
        font-size: 2rem;
    }

    .intensity-value {
        font-size: 1.5rem;
    }
}
