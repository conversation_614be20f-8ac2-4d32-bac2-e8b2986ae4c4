// Integrasi Hardware Sensor dengan Web App
// Untuk SMP PGRI 02 Cigombong

class HardwareSensorIntegration {
    constructor() {
        // IP Address ESP32/ESP8266 (akan diisi otomatis atau manual)
        this.sensorIP = null;
        this.apiEndpoint = '/api/weather';
        this.updateInterval = 5000; // 5 detik
        this.isConnected = false;
        this.retryCount = 0;
        this.maxRetries = 3;
    }
    
    // Auto-discover sensor di jaringan lokal
    async discoverSensor() {
        const commonIPs = [
            '*************', '*************', '*************',
            '*************', '*************', '*************',
            '**********', '**********', '**********'
        ];
        
        console.log('Mencari sensor di jaringan...');
        
        for (const ip of commonIPs) {
            try {
                const response = await fetch(`http://${ip}${this.apiEndpoint}`, {
                    method: 'GET',
                    timeout: 2000
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.location && data.location.includes('PGRI 02 Cigombong')) {
                        this.sensorIP = ip;
                        console.log(`Sensor ditemukan di: ${ip}`);
                        return true;
                    }
                }
            } catch (error) {
                // IP tidak merespons, lanjut ke IP berikutnya
                continue;
            }
        }
        
        console.log('Sensor tidak ditemukan. Gunakan mode simulasi.');
        return false;
    }
    
    // Set IP sensor secara manual
    setSensorIP(ip) {
        this.sensorIP = ip;
        console.log(`Sensor IP diset ke: ${ip}`);
    }
    
    // Ambil data dari sensor hardware
    async getSensorData() {
        if (!this.sensorIP) {
            throw new Error('IP sensor belum diset');
        }
        
        try {
            const response = await fetch(`http://${this.sensorIP}${this.apiEndpoint}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                timeout: 5000
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            this.isConnected = true;
            this.retryCount = 0;
            
            return {
                isRaining: data.isRaining,
                temperature: Math.round(data.temperature),
                humidity: Math.round(data.humidity),
                intensity: data.intensity,
                rawValue: data.rawRainValue,
                timestamp: new Date(),
                source: 'hardware'
            };
            
        } catch (error) {
            this.isConnected = false;
            this.retryCount++;
            console.error('Error mengambil data sensor:', error);
            
            if (this.retryCount >= this.maxRetries) {
                console.log('Beralih ke mode simulasi karena sensor tidak merespons');
                return null;
            }
            
            throw error;
        }
    }
    
    // Mulai monitoring real-time
    async startHardwareMonitoring() {
        // Coba discover sensor otomatis
        const found = await this.discoverSensor();
        
        if (!found && !this.sensorIP) {
            console.log('Sensor tidak ditemukan. Gunakan setSensorIP() untuk set manual.');
            return false;
        }
        
        // Update pertama kali
        await this.updateSensorData();
        
        // Set interval untuk update berkala
        setInterval(async () => {
            await this.updateSensorData();
        }, this.updateInterval);
        
        return true;
    }
    
    // Update data sensor ke aplikasi
    async updateSensorData() {
        try {
            const sensorData = await this.getSensorData();
            
            if (sensorData) {
                // Update variabel global aplikasi
                isRaining = sensorData.isRaining;
                currentTemp = sensorData.temperature;
                currentHumidity = sensorData.humidity;
                currentIntensity = sensorData.intensity;
                
                // Update tampilan
                updateRainStatus();
                updateTemperature();
                updateHumidity();
                updateIntensity();
                updateWeatherIcon();
                
                // Update status koneksi
                this.updateConnectionStatus(true);
                
                // Tambah ke riwayat data
                addDataPoint();
                
                console.log('Data sensor hardware updated:', sensorData);
            }
            
        } catch (error) {
            console.error('Gagal update data sensor:', error);
            this.updateConnectionStatus(false);
            
            // Fallback ke simulasi jika sensor error
            if (this.retryCount >= this.maxRetries) {
                console.log('Menggunakan data simulasi...');
                // Panggil fungsi simulasi original
                updateSensorData();
            }
        }
    }
    
    // Update status koneksi di UI
    updateConnectionStatus(connected) {
        let statusElement = document.getElementById('connection-status');
        
        if (!statusElement) {
            // Buat elemen status jika belum ada
            statusElement = document.createElement('div');
            statusElement.id = 'connection-status';
            statusElement.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                padding: 10px 15px;
                border-radius: 20px;
                font-size: 0.9rem;
                font-weight: 500;
                z-index: 1000;
                transition: all 0.3s ease;
            `;
            document.body.appendChild(statusElement);
        }
        
        if (connected) {
            statusElement.textContent = `🟢 Sensor Terhubung (${this.sensorIP})`;
            statusElement.style.background = 'rgba(76, 175, 80, 0.9)';
            statusElement.style.color = 'white';
        } else {
            statusElement.textContent = '🔴 Sensor Terputus - Mode Simulasi';
            statusElement.style.background = 'rgba(244, 67, 54, 0.9)';
            statusElement.style.color = 'white';
        }
    }
    
    // Test koneksi sensor
    async testConnection() {
        if (!this.sensorIP) {
            return { success: false, message: 'IP sensor belum diset' };
        }
        
        try {
            const data = await this.getSensorData();
            return { 
                success: true, 
                message: 'Koneksi berhasil', 
                data: data 
            };
        } catch (error) {
            return { 
                success: false, 
                message: error.message 
            };
        }
    }
}

// Inisialisasi hardware integration
const hardwareSensor = new HardwareSensorIntegration();

// Fungsi untuk memulai monitoring hardware
async function startHardwareMonitoring() {
    const success = await hardwareSensor.startHardwareMonitoring();
    
    if (success) {
        console.log('Hardware monitoring dimulai');
        // Ganti simulasi dengan data real
        clearInterval(simulationInterval); // Stop simulasi jika ada
    } else {
        console.log('Gagal memulai hardware monitoring, menggunakan simulasi');
    }
}

// Fungsi untuk set IP sensor manual
function setSensorIP(ip) {
    hardwareSensor.setSensorIP(ip);
}

// Fungsi untuk test koneksi
async function testSensorConnection() {
    const result = await hardwareSensor.testConnection();
    console.log('Test koneksi:', result);
    return result;
}

// Export untuk penggunaan di console
window.hardwareSensor = hardwareSensor;
window.startHardwareMonitoring = startHardwareMonitoring;
window.setSensorIP = setSensorIP;
window.testSensorConnection = testSensorConnection;
