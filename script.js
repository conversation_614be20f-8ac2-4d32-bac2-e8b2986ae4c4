// Global variables
let weatherChart;
let sensorData = [];
let isRaining = false;
let currentTemp = 25;
let currentHumidity = 60;
let currentIntensity = 0;

// Educational content
const educationalContent = {
    'rain-formation': {
        title: 'Bagai<PERSON> Hujan Terbentuk?',
        content: `
            <h4>Siklus Air dan Pembentukan Hu<PERSON></h4>
            <p><PERSON><PERSON> terbentuk melalui proses yang disebut siklus air:</p>
            <ol>
                <li><strong>Evaporasi:</strong> Air dari laut, sungai, dan danau menguap karena panas matahari</li>
                <li><strong>Kondensasi:</strong> Uap air naik ke atmosfer dan berubah menjadi tetesan air kecil</li>
                <li><strong>Pembentukan Awan:</strong> Tetesan air berkumpul membentuk awan</li>
                <li><strong>Presipitasi:</strong> Ketika tetesan air menjadi terlalu berat, mereka jatuh sebagai hujan</li>
            </ol>
            <p>Sensor hujan mendeteksi tetesan air ini untuk memberitahu kita kapan hujan dimulai!</p>
        `
    },
    'temperature': {
        title: 'Mengapa Suhu Penting?',
        content: `
            <h4>Pentingnya Suhu dalam Kehidupan</h4>
            <p>Suhu mempengaruhi banyak aspek kehidupan kita:</p>
            <ul>
                <li><strong>Kenyamanan:</strong> Suhu ideal untuk manusia adalah 20-25°C</li>
                <li><strong>Pertumbuhan Tanaman:</strong> Setiap tanaman memiliki suhu optimal untuk tumbuh</li>
                <li><strong>Cuaca:</strong> Suhu mempengaruhi pembentukan awan dan hujan</li>
                <li><strong>Aktivitas:</strong> Suhu menentukan jenis aktivitas yang nyaman dilakukan</li>
            </ul>
            <p>Sensor suhu membantu kita memantau kondisi lingkungan untuk kenyamanan dan keselamatan.</p>
        `
    },
    'humidity': {
        title: 'Apa itu Kelembaban?',
        content: `
            <h4>Memahami Kelembaban Udara</h4>
            <p>Kelembaban adalah jumlah uap air yang ada di udara:</p>
            <ul>
                <li><strong>Kelembaban Rendah (< 40%):</strong> Udara kering, kulit bisa kering</li>
                <li><strong>Kelembaban Ideal (40-60%):</strong> Nyaman untuk manusia</li>
                <li><strong>Kelembaban Tinggi (> 70%):</strong> Udara lembab, terasa gerah</li>
            </ul>
            <p>Kelembaban tinggi sering menandakan akan turun hujan. Sensor kelembaban membantu kita mempersiapkan diri!</p>
        `
    }
};

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    startSensorSimulation();

    // Initialize chart after a small delay to ensure Chart.js is loaded
    setTimeout(() => {
        initializeChart();
    }, 100);

    setupNavigation();
    setupMobileMenu();
});

// Initialize application
function initializeApp() {
    updateDateTime();
    setInterval(updateDateTime, 1000);
    
    // Generate initial data
    generateInitialData();
    updateDataTable();
}

// Update date and time
function updateDateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('id-ID', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
    
    const lastUpdateElement = document.getElementById('lastUpdate');
    if (lastUpdateElement) {
        lastUpdateElement.textContent = timeString;
    }
}

// Start sensor simulation
function startSensorSimulation() {
    // Update sensor data every 5 seconds
    setInterval(updateSensorData, 5000);
    
    // Initial update
    updateSensorData();
}

// Simulate sensor data updates
function updateSensorData() {
    // Simulate rain detection (20% chance)
    const rainChance = Math.random();
    isRaining = rainChance < 0.2;
    
    // Update rain status
    updateRainStatus();
    
    // Simulate temperature (20-35°C)
    currentTemp = Math.round(20 + Math.random() * 15);
    updateTemperature();
    
    // Simulate humidity (30-90%)
    currentHumidity = Math.round(30 + Math.random() * 60);
    updateHumidity();
    
    // Simulate rain intensity if raining
    if (isRaining) {
        currentIntensity = Math.round(20 + Math.random() * 80);
    } else {
        currentIntensity = 0;
    }
    updateIntensity();
    
    // Update weather icon
    updateWeatherIcon();
    
    // Add to data history
    addDataPoint();

    // Update chart if it exists
    if (weatherChart) {
        updateChart();
    }
}

// Update rain status display
function updateRainStatus() {
    const rainText = document.getElementById('rainText');
    const indicator = document.querySelector('.indicator-light');
    
    if (isRaining) {
        rainText.textContent = 'Sedang Hujan';
        indicator.classList.add('raining');
    } else {
        rainText.textContent = 'Tidak Hujan';
        indicator.classList.remove('raining');
    }
}

// Update temperature display
function updateTemperature() {
    const tempValue = document.getElementById('tempValue');
    const tempDesc = document.getElementById('tempDesc');
    
    tempValue.textContent = currentTemp;
    
    if (currentTemp < 20) {
        tempDesc.textContent = 'Dingin';
        tempDesc.style.color = '#007bff';
    } else if (currentTemp < 25) {
        tempDesc.textContent = 'Sejuk';
        tempDesc.style.color = '#28a745';
    } else if (currentTemp < 30) {
        tempDesc.textContent = 'Nyaman';
        tempDesc.style.color = '#28a745';
    } else {
        tempDesc.textContent = 'Panas';
        tempDesc.style.color = '#dc3545';
    }
}

// Update humidity display
function updateHumidity() {
    const humidityValue = document.getElementById('humidityValue');
    const humidityCircle = document.getElementById('humidityCircle');
    
    humidityValue.textContent = currentHumidity;
    
    // Update circle progress
    const circumference = 2 * Math.PI * 45;
    const offset = circumference - (currentHumidity / 100) * circumference;
    humidityCircle.style.strokeDashoffset = offset;
}

// Update rain intensity
function updateIntensity() {
    const intensityValue = document.getElementById('intensityValue');
    const intensityFill = document.getElementById('intensityFill');
    
    intensityValue.textContent = currentIntensity;
    intensityFill.style.width = currentIntensity + '%';
}

// Update weather icon
function updateWeatherIcon() {
    const weatherIcon = document.getElementById('weatherIcon');
    const icon = weatherIcon.querySelector('i');
    
    if (isRaining) {
        icon.className = 'fas fa-cloud-rain';
        icon.style.color = '#6c757d';
    } else if (currentTemp > 28) {
        icon.className = 'fas fa-sun';
        icon.style.color = '#ffd700';
    } else {
        icon.className = 'fas fa-cloud-sun';
        icon.style.color = '#87ceeb';
    }
}

// Generate initial historical data
function generateInitialData() {
    const now = new Date();
    for (let i = 23; i >= 0; i--) {
        const time = new Date(now.getTime() - i * 60 * 60 * 1000);
        const rain = Math.random() < 0.3;
        sensorData.push({
            time: time,
            isRaining: rain,
            intensity: rain ? Math.round(20 + Math.random() * 60) : 0,
            temperature: Math.round(20 + Math.random() * 15),
            humidity: Math.round(40 + Math.random() * 50)
        });
    }
}

// Add new data point
function addDataPoint() {
    const now = new Date();
    sensorData.push({
        time: now,
        isRaining: isRaining,
        intensity: currentIntensity,
        temperature: currentTemp,
        humidity: currentHumidity
    });
    
    // Keep only last 24 hours of data
    if (sensorData.length > 24) {
        sensorData.shift();
    }
    
    updateDataTable();
}

// Update data table
function updateDataTable() {
    const tableBody = document.getElementById('dataTableBody');
    tableBody.innerHTML = '';
    
    // Show last 10 entries
    const recentData = sensorData.slice(-10).reverse();
    
    recentData.forEach(data => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${data.time.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' })}</td>
            <td><span class="status-badge ${data.isRaining ? 'raining' : 'clear'}">${data.isRaining ? 'Hujan' : 'Cerah'}</span></td>
            <td>${data.intensity}%</td>
            <td>${data.temperature}°C</td>
            <td>${data.humidity}%</td>
        `;
        tableBody.appendChild(row);
    });
}

// Initialize chart
function initializeChart() {
    const chartElement = document.getElementById('weatherChart');

    // Check if chart element exists
    if (!chartElement) {
        console.log('Chart element not found');
        return;
    }

    const ctx = chartElement.getContext('2d');

    try {
        weatherChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Suhu (°C)',
                    data: [],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Kelembaban (%)',
                    data: [],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Intensitas Hujan (%)',
                    data: [],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: 'Data Sensor 24 Jam Terakhir'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });

        updateChart();
    } catch (error) {
        console.error('Error initializing chart:', error);
    }
}

// Update chart with current data
function updateChart() {
    // Check if chart exists before updating
    if (!weatherChart) {
        console.log('Chart not initialized yet');
        return;
    }

    const labels = sensorData.map(data =>
        data.time.toLocaleTimeString('id-ID', { hour: '2-digit', minute: '2-digit' })
    );

    weatherChart.data.labels = labels;
    weatherChart.data.datasets[0].data = sensorData.map(data => data.temperature);
    weatherChart.data.datasets[1].data = sensorData.map(data => data.humidity);
    weatherChart.data.datasets[2].data = sensorData.map(data => data.intensity);

    weatherChart.update();
}

// Setup navigation
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all links
            navLinks.forEach(l => l.classList.remove('active'));

            // Add active class to clicked link
            this.classList.add('active');

            // Close mobile menu if open
            closeMobileMenu();

            // Smooth scroll to section
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);

            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Setup mobile menu
function setupMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const nav = document.querySelector('.nav');

    if (mobileMenuBtn && nav) {
        mobileMenuBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleMobileMenu();
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenuBtn.contains(e.target) && !nav.contains(e.target)) {
                closeMobileMenu();
            }
        });

        // Close menu on window resize if screen becomes larger
        window.addEventListener('resize', function() {
            if (window.innerWidth > 768) {
                closeMobileMenu();
            }
        });
    }
}

// Toggle mobile menu
function toggleMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const nav = document.querySelector('.nav');

    if (mobileMenuBtn && nav) {
        const isActive = nav.classList.contains('active');

        if (isActive) {
            closeMobileMenu();
        } else {
            openMobileMenu();
        }
    }
}

// Open mobile menu
function openMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const nav = document.querySelector('.nav');

    if (mobileMenuBtn && nav) {
        mobileMenuBtn.classList.add('active');
        nav.classList.add('active');
        mobileMenuBtn.setAttribute('aria-expanded', 'true');

        // Prevent body scroll when menu is open
        document.body.style.overflow = 'hidden';
    }
}

// Close mobile menu
function closeMobileMenu() {
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    const nav = document.querySelector('.nav');

    if (mobileMenuBtn && nav) {
        mobileMenuBtn.classList.remove('active');
        nav.classList.remove('active');
        mobileMenuBtn.setAttribute('aria-expanded', 'false');

        // Restore body scroll
        document.body.style.overflow = '';
    }
}

// Modal functions
function showModal(contentType) {
    const modal = document.getElementById('educationModal');
    const modalContent = document.getElementById('modalContent');
    
    const content = educationalContent[contentType];
    if (content) {
        modalContent.innerHTML = `
            <h3>${content.title}</h3>
            ${content.content}
        `;
        modal.style.display = 'block';
    }
}

function closeModal() {
    const modal = document.getElementById('educationModal');
    modal.style.display = 'none';
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('educationModal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

// Add CSS for status badges
const style = document.createElement('style');
style.textContent = `
    .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 15px;
        font-size: 0.875rem;
        font-weight: 500;
    }
    
    .status-badge.raining {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    .status-badge.clear {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
    }
`;
document.head.appendChild(style);
