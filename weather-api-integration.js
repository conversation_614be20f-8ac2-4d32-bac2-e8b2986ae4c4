// Integrasi dengan API Cuaca Real
// Contoh menggunakan OpenWeatherMap API

class WeatherAPIIntegration {
    constructor() {
        // Ganti dengan API key Anda dari openweathermap.org
        this.apiKey = 'YOUR_API_KEY_HERE';
        this.baseUrl = 'https://api.openweathermap.org/data/2.5';
        
        // Koordinat Cigombong, Bogor (perkiraan)
        this.lat = -6.4175;
        this.lon = 106.8467;
        
        this.updateInterval = 10 * 60 * 1000; // Update setiap 10 menit
    }
    
    // Ambil data cuaca saat ini
    async getCurrentWeather() {
        try {
            const response = await fetch(
                `${this.baseUrl}/weather?lat=${this.lat}&lon=${this.lon}&appid=${this.apiKey}&units=metric&lang=id`
            );
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return this.processWeatherData(data);
        } catch (error) {
            console.error('Error fetching weather data:', error);
            return null;
        }
    }
    
    // Proses data dari API menjadi format aplikasi
    processWeatherData(data) {
        const isRaining = data.weather[0].main.toLowerCase().includes('rain') || 
                         data.weather[0].description.toLowerCase().includes('hujan');
        
        // Hitung intensitas hujan berdasarkan data
        let rainIntensity = 0;
        if (data.rain) {
            const rainVolume = data.rain['1h'] || data.rain['3h'] || 0;
            rainIntensity = Math.min(Math.round(rainVolume * 10), 100);
        }
        
        return {
            isRaining: isRaining,
            temperature: Math.round(data.main.temp),
            humidity: data.main.humidity,
            intensity: rainIntensity,
            description: data.weather[0].description,
            windSpeed: data.wind.speed,
            pressure: data.main.pressure,
            visibility: data.visibility / 1000, // km
            timestamp: new Date()
        };
    }
    
    // Mulai monitoring otomatis
    startRealTimeMonitoring() {
        // Update pertama kali
        this.updateWeatherData();
        
        // Set interval untuk update berkala
        setInterval(() => {
            this.updateWeatherData();
        }, this.updateInterval);
    }
    
    // Update data cuaca ke aplikasi
    async updateWeatherData() {
        const weatherData = await this.getCurrentWeather();
        
        if (weatherData) {
            // Update variabel global aplikasi
            isRaining = weatherData.isRaining;
            currentTemp = weatherData.temperature;
            currentHumidity = weatherData.humidity;
            currentIntensity = weatherData.intensity;
            
            // Update tampilan
            updateRainStatus();
            updateTemperature();
            updateHumidity();
            updateIntensity();
            updateWeatherIcon();
            
            // Tambah ke riwayat data
            sensorData.push({
                time: weatherData.timestamp,
                isRaining: weatherData.isRaining,
                intensity: weatherData.intensity,
                temperature: weatherData.temperature,
                humidity: weatherData.humidity
            });
            
            // Batasi data (24 jam terakhir)
            if (sensorData.length > 144) { // 24 jam * 6 update/jam
                sensorData.shift();
            }
            
            updateDataTable();
            if (weatherChart) {
                updateChart();
            }
            
            console.log('Weather data updated:', weatherData);
        }
    }
}

// Inisialisasi API integration
const weatherAPI = new WeatherAPIIntegration();

// Ganti fungsi simulasi dengan data real
function startRealWeatherMonitoring() {
    weatherAPI.startRealTimeMonitoring();
}

// Fungsi untuk mendapatkan API key gratis
function getAPIKeyInstructions() {
    return `
    Cara mendapatkan API Key gratis:
    
    1. Kunjungi: https://openweathermap.org/api
    2. Daftar akun gratis
    3. Verifikasi email
    4. Dapatkan API key dari dashboard
    5. Ganti 'YOUR_API_KEY_HERE' dengan API key Anda
    
    Gratis: 1000 calls/day, update setiap 10 menit
    `;
}
